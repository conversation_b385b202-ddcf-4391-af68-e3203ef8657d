// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  sessions      Session[]
  accounts      Account[]
  watched       Watched[]
  lists         List[]

  name  String
  image String?

  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  userId                String
  accountId             String
  providerId            String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  username              String
  password              String?
  name                  String?
  image                 String?
  about                 String
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([providerId, accountId])
  @@map("account")
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, value])
  @@map("verification")
}

model Media {
  id        String     @id @default(cuid())
  tmdbId    Int        @unique
  title     String
  type      MediaType
  posterUrl String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  watched   Watched[]
  listItems ListItem[]

  @@map("media")
}

model Watched {
  id        String   @id @default(cuid())
  userId    String
  mediaId   String
  watchDate DateTime @default(now())
  rating    Float?
  review    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  media     Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([userId, mediaId])
  @@map("watched")
}

model List {
  id          String     @id @default(cuid())
  userId      String
  name        String
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       ListItem[]

  @@map("list")
}

model ListItem {
  id        String   @id @default(cuid())
  listId    String
  mediaId   String
  createdAt DateTime @default(now())
  list      List     @relation(fields: [listId], references: [id], onDelete: Cascade)
  media     Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)

  @@unique([listId, mediaId])
  @@map("list_item")
}

enum MediaType {
  MOVIE
  TV_SHOW
}
