import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateAccountDto } from '../auth/dto/auth.dto';
import { UserProfile } from './account.types';

@Injectable()
export class AccountService {
  constructor(private prisma: PrismaService) {}

  async getUserProfile(userId: string): Promise<UserProfile> {
    const userWithCounts = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        accounts: true,
        _count: {
          select: {
            watched: true,
            lists: true,
          },
        },
      },
    });

    if (!userWithCounts) {
      throw new NotFoundException('User not found');
    }

    // Extract the user data explicitly
    const { accounts, _count, ...user } = userWithCounts;

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.image || undefined,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      stats: {
        watchedCount: _count.watched,
        listsCount: _count.lists,
      },
      accounts: accounts.map((account) => ({
        id: account.id,
        userId: account.userId,
        accountId: account.accountId,
        providerId: account.providerId,
        accessToken: account.accessToken || undefined,
        refreshToken: account.refreshToken || undefined,
        idToken: account.idToken || undefined,
        accessTokenExpiresAt: account.accessTokenExpiresAt || undefined,
        refreshTokenExpiresAt: account.refreshTokenExpiresAt || undefined,
        scope: account.scope || undefined,
        username: account.username,
        password: account.password || undefined,
        name: account.name || undefined,
        image: account.image || undefined,
        about: account.about,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      })),
    };
  }

  async updateUserProfile(userId: string, updateAccountDto: UpdateAccountDto) {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Prepare update data - only include fields that exist in the User model
    const updateData: { name?: string; image?: string } = {};

    if (updateAccountDto.name !== undefined) {
      updateData.name = updateAccountDto.name;
    }

    if (updateAccountDto.image !== undefined) {
      updateData.image = updateAccountDto.image;
    }

    // Update user profile
    return this.prisma.user.update({
      where: { id: userId },
      data: updateData,
    });
  }

  async deleteUserAccount(userId: string) {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Delete user (cascade will handle related records)
    return this.prisma.user.delete({
      where: { id: userId },
    });
  }
}
