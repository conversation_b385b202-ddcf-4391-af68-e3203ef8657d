export interface Account {
  id: string;
  userId: string;
  accountId: string;
  providerId: string;
  accessToken?: string;
  refreshToken?: string;
  idToken?: string;
  accessTokenExpiresAt?: Date;
  refreshTokenExpiresAt?: Date;
  scope?: string;
  username: string;
  password?: string;
  name?: string;
  image?: string;
  about: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  image?: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  stats: {
    watchedCount: number;
    listsCount: number;
  };
  accounts: Account[];
}

export interface AccountPreferences {
  id: string;
  userId: string;
  theme: 'light' | 'dark' | 'system';
  language: string;
  emailNotifications: boolean;
  publicProfile: boolean;
  showWatchedStats: boolean;
  createdAt: Date;
  updatedAt: Date;
}
