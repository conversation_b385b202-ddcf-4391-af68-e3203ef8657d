import { <PERSON>, All, Req, Res, Inject } from '@nestjs/common';
import { Request, Response } from 'express';
import type { Auth } from 'better-auth';
import { AUTH_INSTANCE_KEY } from './symbols';

@Controller('auth')
export class AuthController {
  constructor(@Inject(AUTH_INSTANCE_KEY) private readonly auth: Auth) {}

  @All('*')
  async handleAuth(@Req() req: Request, @Res() res: Response) {
    const webRequest = new Request(req.url, {
      method: req.method,
      headers: req.headers as HeadersInit,
      body:
        req.method !== 'GET' && req.method !== 'HEAD'
          ? JSON.stringify(req.body)
          : undefined,
    });

    const response = await this.auth.handler(webRequest);

    // Set headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });

    // Set status code
    res.status(response.status);

    // Send body
    if (response.body) {
      const body = await response.text();
      res.send(body);
    } else {
      res.end();
    }
  }
}
