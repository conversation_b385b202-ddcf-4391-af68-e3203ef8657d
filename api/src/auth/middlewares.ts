import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to skip body parsing for auth routes
 * This is needed because better-auth handles its own body parsing
 */
@Injectable()
export class SkipBodyParsingMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    (req as any).skipBodyParser = true;
    next();
  }
}
