# Better Auth NestJS Implementation

This directory contains a comprehensive implementation of [Better Auth](https://better-auth.dev/) integrated with NestJS, adapted from the [nestjs-better-auth-template](https://github.com/laakal/nestjs-better-auth-template) but repurposed for Prisma adapter with SQLite instead of MongoDB.

## Features

- ✅ **Prisma + SQLite Integration** - Uses Prisma adapter with SQLite database
- ✅ **Dynamic Module Configuration** - Configurable auth module with `forRoot()` pattern
- ✅ **Enhanced Decorators** - Multiple decorators for extracting user data
- ✅ **Public Route Support** - `@Public()` decorator to skip authentication
- ✅ **Social Providers** - Support for GitHub and Google OAuth (configurable)
- ✅ **Cross-domain Sessions** - Production-ready cross-subdomain cookie support
- ✅ **Exception Handling** - Custom exception filter for auth errors
- ✅ **Middleware Support** - Body parsing middleware and hooks system
- ✅ **Service Layer** - Auth service for programmatic auth operations

## File Structure

```
src/auth/
├── auth.module.ts                 # Main auth module with forRoot() configuration
├── auth.service.ts                # Service layer for auth operations
├── auth.controller.ts             # Auth route handler (catch-all for better-auth)
├── auth.guard.ts                  # Authentication guard with public route support
├── auth.config.ts                 # Better-auth configuration
├── auth.decorators.ts             # Parameter decorators for extracting auth data
├── auth.types.ts                  # TypeScript type definitions
├── symbols.ts                     # DI tokens and hook decorators
├── middlewares.ts                 # Custom middlewares
├── api-error-exception-filter.ts  # Exception filter for auth errors
├── auth-example.controller.ts     # Example usage patterns
└── README.md                      # This file
```

## Usage Examples

### 1. Basic Protected Route

```typescript
@Controller('api/protected')
export class ProtectedController {
  @Get('data')
  @UseGuards(AuthGuard)
  getData(@CurrentUser() user: AuthUser) {
    return { message: 'Protected data', user };
  }
}
```

### 2. Public Route

```typescript
@Controller('api/public')
export class PublicController {
  @Get('info')
  @Public() // Skip authentication
  getInfo() {
    return { message: 'Public information' };
  }
}
```

### 3. Using Different Decorators

```typescript
@Controller('api/user')
@UseGuards(AuthGuard)
export class UserController {
  @Get('profile')
  getProfile(
    @CurrentUser() user: AuthUser,
    @UserId() userId: string,
    @Session() session: AuthSession,
  ) {
    return { user, userId, session };
  }
}
```

### 4. Using Auth Service

```typescript
@Injectable()
export class SomeService {
  constructor(private authService: AuthService) {}

  async validateUser(headers: Record<string, any>) {
    const user = await this.authService.getUser(headers);
    return user;
  }
}
```

## Configuration

### Environment Variables

```env
# Required
DATABASE_URL="file:./dev.db"
BETTER_AUTH_SECRET="your-secret-key"
BETTER_AUTH_URL="http://localhost:4020"

# Optional
TRUSTED_ORIGINS="http://localhost:3000,http://localhost:3001"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Module Configuration

```typescript
@Module({
  imports: [
    AuthModule.forRoot({
      disableExceptionFilter: false,
      disableTrustedOriginsCors: false,
      disableBodyParser: false,
    }),
  ],
})
export class AppModule {}
```

## Available Decorators

- `@CurrentUser()` - Get the current authenticated user
- `@UserId()` - Get just the user ID
- `@Session()` - Get the full session object
- `@Public()` - Mark routes as public (skip authentication)

## Auth Service Methods

- `getAuthInstance()` - Get the better-auth instance
- `getSession(headers)` - Get session from request headers
- `getUser(headers)` - Get user from request headers
- `signOut(headers)` - Sign out user
- `verifySession(token)` - Verify a session token

## Frontend Integration

Use `@better-auth/react` for React/Next.js integration:

```typescript
import { createAuthClient } from 'better-auth/react';

export const { signIn, signUp, signOut, useSession } = createAuthClient({
  baseURL: "http://localhost:4020", // Your API base URL
});
```

## Production Considerations

1. **Environment Variables**: Set proper secrets and URLs
2. **CORS Configuration**: Configure trusted origins properly
3. **Cross-domain Cookies**: Set `CROSS_DOMAIN_ORIGIN` for subdomain support
4. **SSL/HTTPS**: Enable secure cookies in production
5. **Email Verification**: Enable email verification for production

## Migration from MongoDB Template

This implementation has been adapted from the MongoDB template with these key changes:

1. **Database Adapter**: Changed from `mongodbAdapter` to `prismaAdapter`
2. **Database Provider**: Changed from MongoDB to SQLite
3. **Schema**: Uses Prisma schema instead of MongoDB collections
4. **Configuration**: Adapted for Prisma client integration

The core functionality and API remain the same, making it easy to migrate existing better-auth implementations.
