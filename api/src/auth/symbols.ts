/**
 * Dependency injection tokens and constants for the Auth module
 */

export const AUTH_INSTANCE_KEY = Symbol('AUTH_INSTANCE');
export const AUTH_MODULE_OPTIONS_KEY = Symbol('AUTH_MODULE_OPTIONS');
export const HOOK_KEY = Symbol('HOOK');
export const BEFORE_HOOK_KEY = Symbol('BEFORE_HOOK');
export const AFTER_HOOK_KEY = Symbol('AFTER_HOOK');

/**
 * Decorator for marking methods as auth hooks
 */
export function Hook(path: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    Reflect.defineMetadata(HOOK_KEY, true, target.constructor);
    Reflect.defineMetadata(BEFORE_HOOK_KEY, path, descriptor.value);
  };
}

/**
 * Decorator for marking methods as before hooks
 */
export function BeforeHook(path: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    Reflect.defineMetadata(HOOK_KEY, true, target.constructor);
    Reflect.defineMetadata(BEFORE_HOOK_KEY, path, descriptor.value);
  };
}

/**
 * Decorator for marking methods as after hooks
 */
export function AfterHook(path: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    Reflect.defineMetadata(HOOK_KEY, true, target.constructor);
    Reflect.defineMetadata(AFTER_HOOK_KEY, path, descriptor.value);
  };
}
