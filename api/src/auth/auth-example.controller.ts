import { Controller, Get, Post, UseGuards, Body } from '@nestjs/common';
import { AuthGuard } from './auth.guard';
import { CurrentUser, UserId, Session, Public } from './auth.decorators';
import { AuthService } from './auth.service';
import type { AuthUser, AuthSession } from './auth.types';

/**
 * Example controller demonstrating how to use the enhanced better-auth implementation
 * This controller shows various authentication patterns and decorators
 */
@Controller('auth-example')
export class AuthExampleController {
  constructor(private readonly authService: AuthService) {}

  /**
   * Public route - no authentication required
   */
  @Get('public')
  @Public()
  getPublicData() {
    return {
      message: 'This is a public endpoint, no authentication required',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Protected route - requires authentication
   */
  @Get('protected')
  @UseGuards(AuthGuard)
  getProtectedData(@CurrentUser() user: AuthUser) {
    return {
      message: 'This is a protected endpoint',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get current user information
   */
  @Get('me')
  @UseGuards(AuthGuard)
  getCurrentUser(@CurrentUser() user: AuthUser, @Session() session: AuthSession) {
    return {
      user,
      session: {
        id: session.session.id,
        expiresAt: session.session.expiresAt,
        createdAt: session.session.createdAt,
      },
    };
  }

  /**
   * Get just the user ID
   */
  @Get('user-id')
  @UseGuards(AuthGuard)
  getUserId(@UserId() userId: string) {
    return {
      userId,
      message: 'This endpoint demonstrates the @UserId() decorator',
    };
  }

  /**
   * Example of a route that uses the auth service
   */
  @Post('verify-session')
  @UseGuards(AuthGuard)
  async verifySession(@Body() body: { token?: string }, @CurrentUser() user: AuthUser) {
    if (body.token) {
      const session = await this.authService.verifySession(body.token);
      return {
        valid: !!session,
        session,
      };
    }

    return {
      message: 'Current session is valid',
      user,
    };
  }

  /**
   * Example of getting auth instance
   */
  @Get('auth-info')
  @UseGuards(AuthGuard)
  getAuthInfo() {
    const authInstance = this.authService.getAuthInstance();
    return {
      message: 'Auth instance information',
      baseURL: authInstance.options.baseURL,
      trustedOrigins: authInstance.options.trustedOrigins,
      // Don't expose sensitive information like secrets
    };
  }
}
