import { Injectable, Inject } from '@nestjs/common';
import type { Auth } from 'better-auth';
import { AUTH_INSTANCE_KEY } from './symbols';
import type { AuthSession, AuthUser } from './auth.types';

/**
 * Service for handling authentication operations
 */
@Injectable()
export class AuthService {
  constructor(
    @Inject(AUTH_INSTANCE_KEY) private readonly auth: Auth,
  ) {}

  /**
   * Get the auth instance
   */
  getAuthInstance(): Auth {
    return this.auth;
  }

  /**
   * Get session from headers
   */
  async getSession(headers: Record<string, any>): Promise<AuthSession | null> {
    try {
      const session = await this.auth.api.getSession({ headers });
      return session;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user from session
   */
  async getUser(headers: Record<string, any>): Promise<AuthUser | null> {
    const session = await this.getSession(headers);
    return session?.user || null;
  }

  /**
   * Sign out user
   */
  async signOut(headers: Record<string, any>): Promise<void> {
    await this.auth.api.signOut({ headers });
  }

  /**
   * Verify session token
   */
  async verifySession(token: string): Promise<AuthSession | null> {
    try {
      // This would need to be implemented based on better-auth's session verification
      // For now, we'll use the headers approach
      return await this.getSession({ authorization: `Bearer ${token}` });
    } catch (error) {
      return null;
    }
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId: string): Promise<any[]> {
    // This would need to be implemented based on your requirements
    // You might want to query the database directly for this
    return [];
  }

  /**
   * Revoke all sessions for a user
   */
  async revokeAllUserSessions(userId: string): Promise<void> {
    // This would need to be implemented based on your requirements
    // You might want to delete sessions from the database directly
  }
}
